const MangaContentDownloader = require('./download-manga-content');
const path = require('path');

async function testImageFiltering() {
    const downloader = new MangaContentDownloader();
    
    try {
        console.log('🚀 初始化下载器...');
        await downloader.init();
        
        console.log('🧪 测试图片过滤和保存功能');
        console.log('🔧 修复内容:');
        console.log('   - 过滤 .mh_loaderr 且 display:none 的脏数据');
        console.log('   - 检测加载失败的图片并刷新重试');
        console.log('   - 修复图片保存0张的问题');
        console.log('   - 详细的调试信息输出');
        console.log('');
        
        // 测试单个章节的图片过滤和下载
        const testMangaId = 'ap101511'; // 请替换为实际的漫画ID
        const testMangaName = '测试漫画';
        const testChapter = 1;
        
        console.log(`📖 测试漫画: ${testMangaName} (ID: ${testMangaId}) 第${testChapter}章`);
        
        // 访问章节页面
        const chapterUrl = `https://www.colamanga.com/manga-${testMangaId}/1/${testChapter}.html`;
        console.log(`🔗 访问章节: ${chapterUrl}`);
        
        await downloader.page.goto(chapterUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
        
        // 记录开始时间
        const startTime = Date.now();
        
        // 测试图片数量统计（包含过滤功能）
        console.log('\n📊 第一步：测试图片数量统计和过滤...');
        const webImageCount = await downloader.getWebImageCount();
        console.log(`✅ 网页图片数量统计完成: ${webImageCount} 张`);
        
        // 测试图片下载
        console.log('\n💾 第二步：测试图片下载...');
        
        // 创建测试目录
        const testDir = path.join(downloader.outputDir, 'test-filtering');
        const chapterDir = path.join(testDir, `第${testChapter}章-测试`);
        await require('fs-extra').ensureDir(chapterDir);
        
        // 下载图片
        const downloadedCount = await downloader.downloadPageImages(chapterDir);
        
        // 记录结束时间
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log(`\n📊 测试结果:`);
        console.log(`   ⏱️ 总耗时: ${duration.toFixed(2)} 秒`);
        console.log(`   📊 网页图片数量: ${webImageCount} 张`);
        console.log(`   💾 成功下载: ${downloadedCount} 张`);
        console.log(`   📁 保存目录: ${chapterDir}`);
        
        if (downloadedCount > 0) {
            console.log(`\n✅ 测试成功！图片过滤和保存功能正常工作`);
            
            // 检查文件
            const fs = require('fs-extra');
            const files = await fs.readdir(chapterDir);
            const imageFiles = files.filter(f => f.endsWith('.png') || f.endsWith('.jpg') || f.endsWith('.jpeg'));
            
            console.log(`📁 实际保存的文件:`);
            imageFiles.slice(0, 5).forEach(file => {
                console.log(`   - ${file}`);
            });
            
            if (imageFiles.length > 5) {
                console.log(`   ... 还有 ${imageFiles.length - 5} 个文件`);
            }
            
        } else {
            console.log(`\n⚠️ 测试发现问题：没有下载到任何图片`);
            console.log(`可能的原因:`);
            console.log(`   - 所有图片都被过滤掉了`);
            console.log(`   - 图片还未加载完成`);
            console.log(`   - 页面结构发生变化`);
        }
        
        // 额外的调试信息
        console.log(`\n🔍 详细调试信息:`);
        const debugInfo = await downloader.page.evaluate(() => {
            const comicPics = document.querySelectorAll('.mh_comicpic');
            const info = {
                totalElements: comicPics.length,
                withP: 0,
                withImg: 0,
                withBlob: 0,
                withError: 0,
                errorVisible: 0,
                samples: []
            };
            
            for (let i = 0; i < Math.min(comicPics.length, 3); i++) {
                const pic = comicPics[i];
                const img = pic.querySelector('img');
                const pValue = pic.getAttribute('p');
                const errorElement = pic.querySelector('.mh_loaderr');
                
                if (pValue) info.withP++;
                if (img) info.withImg++;
                if (img && img.src && img.src.startsWith('blob:')) info.withBlob++;
                if (errorElement) {
                    info.withError++;
                    const errorStyle = window.getComputedStyle(errorElement);
                    if (errorStyle.display !== 'none') info.errorVisible++;
                }
                
                info.samples.push({
                    index: i,
                    p: pValue,
                    hasImg: !!img,
                    imgSrc: img ? img.src.substring(0, 50) + '...' : 'no img',
                    hasError: !!errorElement,
                    errorVisible: errorElement ? window.getComputedStyle(errorElement).display !== 'none' : false
                });
            }
            
            return info;
        });
        
        console.log(`   📊 元素统计:`);
        console.log(`      - 总元素: ${debugInfo.totalElements}`);
        console.log(`      - 带p属性: ${debugInfo.withP}`);
        console.log(`      - 有img元素: ${debugInfo.withImg}`);
        console.log(`      - 有blob图片: ${debugInfo.withBlob}`);
        console.log(`      - 有错误元素: ${debugInfo.withError}`);
        console.log(`      - 错误可见: ${debugInfo.errorVisible}`);
        console.log(`   📄 前3个元素样本:`, debugInfo.samples);
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await downloader.close();
    }
}

// 运行测试
if (require.main === module) {
    testImageFiltering().catch(console.error);
}

module.exports = testImageFiltering;
