const MangaContentDownloader = require('./download-manga-content.js');

async function testOptimizedDownload() {
    const downloader = new MangaContentDownloader();

    try {
        await downloader.init();

        console.log('🧪 测试优化后的下载逻辑...');
        console.log('📋 优化内容:');
        console.log('   - 减少图片加载等待时间');
        console.log('   - 简化验证和重试逻辑');
        console.log('   - 使用更宽松的完成条件');
        console.log('   - 避免重复的页面刷新');
        console.log('');

        // 测试单个章节下载
        const startTime = Date.now();
        const success = await downloader.downloadMangaContent('pt483529', '公爵的契约未婚妻', 3);
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;

        console.log('');
        console.log('📊 测试结果:');
        console.log(`   - 下载结果: ${success ? '✅ 成功' : '❌ 失败'}`);
        console.log(`   - 耗时: ${duration.toFixed(1)} 秒`);

        if (success) {
            console.log('✅ 优化后的下载逻辑测试成功！');
            console.log('🎯 主要改进:');
            console.log('   - 减少了重复的页面刷新');
            console.log('   - 缩短了图片加载等待时间');
            console.log('   - 使用更智能的完整性检查');
        } else {
            console.log('❌ 优化后的下载逻辑测试失败！');
        }

    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await downloader.close();
    }
}

// 运行测试
if (require.main === module) {
    testOptimizedDownload().catch(console.error);
}

module.exports = testOptimizedDownload;
