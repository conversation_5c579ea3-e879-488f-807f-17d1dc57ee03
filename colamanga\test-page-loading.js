const { chromium } = require('playwright');

async function testPageLoading() {
    console.log('🧪 测试页面加载逻辑...');
    
    const context = await chromium.launchPersistentContext('', {
        headless: false,
        channel: 'chrome'
    });
    
    const page = await context.newPage();
    
    try {
        // 导航到测试页面
        console.log('🔗 导航到测试页面...');
        await page.goto('https://www.colamanga.com/manga-21842/1/10.html', {
            waitUntil: 'domcontentloaded',
            timeout: 60000
        });
        
        // 等待基本内容加载
        await page.waitForSelector('.mh_comicpic', { timeout: 15000 });
        
        // 测试滚动逻辑
        console.log('📜 开始测试滚动逻辑...');
        let lastElementCount = 0;
        let stableCount = 0;
        let scrollAttempts = 0;
        const maxScrollAttempts = 50;
        const stableThreshold = 5;
        
        while (scrollAttempts < maxScrollAttempts) {
            // 滚动到页面底部
            await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });
            
            // 等待懒加载
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // 检查元素数量
            const currentElementCount = await page.evaluate(() => {
                const comicPics = document.querySelectorAll('.mh_comicpic');
                const validElements = Array.from(comicPics).filter(pic => {
                    const pValue = pic.getAttribute('p');
                    const errorElement = pic.querySelector('.mh_loaderr');
                    
                    if (errorElement) {
                        const errorStyle = window.getComputedStyle(errorElement);
                        if (errorStyle.display !== 'none') return false;
                    }
                    
                    return pValue !== null;
                });
                return validElements.length;
            });
            
            console.log(`📊 滚动第${scrollAttempts + 1}次: 发现 ${currentElementCount} 个有效元素`);
            
            if (currentElementCount === lastElementCount) {
                stableCount++;
                console.log(`⏳ 元素数量稳定 ${stableCount}/${stableThreshold} 次`);
                
                if (stableCount >= stableThreshold) {
                    console.log(`✅ 滚动完成，共发现 ${currentElementCount} 个有效元素`);
                    break;
                }
            } else {
                console.log(`📈 发现新元素: ${lastElementCount} → ${currentElementCount} (+${currentElementCount - lastElementCount})`);
                stableCount = 0;
                lastElementCount = currentElementCount;
            }
            
            scrollAttempts++;
        }
        
        // 测试blob加载检测
        console.log('🖼️ 开始测试blob加载检测...');
        let attempts = 0;
        const maxAttempts = 10;
        
        while (attempts < maxAttempts) {
            const loadStatus = await page.evaluate(() => {
                const comicPics = document.querySelectorAll('.mh_comicpic');
                let validImages = 0;
                let blobImages = 0;
                let httpImages = 0;
                let noSrcImages = 0;

                for (let i = 0; i < comicPics.length; i++) {
                    const pic = comicPics[i];
                    const pValue = pic.getAttribute('p');
                    const img = pic.querySelector('img');
                    const errorElement = pic.querySelector('.mh_loaderr');

                    if (errorElement) {
                        const errorStyle = window.getComputedStyle(errorElement);
                        if (errorStyle.display !== 'none') continue;
                    }

                    if (pValue && img) {
                        validImages++;

                        if (!img.src) {
                            noSrcImages++;
                        } else if (img.src.startsWith('blob:')) {
                            blobImages++;
                        } else if (img.src.startsWith('http')) {
                            httpImages++;
                        }
                    }
                }

                return {
                    valid: validImages,
                    blob: blobImages,
                    http: httpImages,
                    noSrc: noSrcImages,
                    blobRate: validImages > 0 ? (blobImages / validImages) * 100 : 0
                };
            });

            console.log(`⏳ 第${attempts + 1}次检查: ${loadStatus.blob}/${loadStatus.valid} 张图片已转为blob (${loadStatus.blobRate.toFixed(1)}%)`);
            console.log(`   - blob图片: ${loadStatus.blob}, http图片: ${loadStatus.http}, 无src: ${loadStatus.noSrc}`);

            if (loadStatus.blobRate >= 95) {
                console.log(`✅ blob加载完成，加载率: ${loadStatus.blobRate.toFixed(1)}%`);
                break;
            }

            await new Promise(resolve => setTimeout(resolve, 3000));
            attempts++;
        }
        
        console.log('🎯 测试完成');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        await context.close();
    }
}

// 运行测试
testPageLoading();
