{"phones": [{"phone": "19239094464", "tokenId": 6, "registeredAt": "2025-07-11T03:23:39.454Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:13.374Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19292390449", "tokenId": 7, "registeredAt": "2025-07-11T03:24:10.028Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:14.191Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19289097564", "tokenId": 8, "registeredAt": "2025-07-11T03:25:46.197Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:14.968Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19292447202", "tokenId": 9, "registeredAt": "2025-07-11T03:26:11.154Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:15.769Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19212590157", "tokenId": 10, "registeredAt": "2025-07-11T03:27:58.190Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:16.605Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19292543836", "tokenId": 11, "registeredAt": "2025-07-11T03:32:07.753Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:17.387Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259464063", "tokenId": 8, "registeredAt": "2025-07-22T11:12:42.935Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:18.175Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259463059", "tokenId": 9, "registeredAt": "2025-07-22T11:13:08.336Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:19.016Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19269468919", "tokenId": 10, "registeredAt": "2025-07-22T11:13:34.994Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:19.862Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "13097461853", "tokenId": 11, "registeredAt": "2025-07-22T11:13:56.587Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:20.652Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259464256", "tokenId": 12, "registeredAt": "2025-07-22T11:14:21.989Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:21.473Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259464036", "tokenId": 13, "registeredAt": "2025-07-22T11:14:48.178Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:22.313Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19289648504", "tokenId": 14, "registeredAt": "2025-07-23T09:03:27.457Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:23.104Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19211649140", "tokenId": 15, "registeredAt": "2025-07-23T09:04:37.832Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:23.959Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "16237200807", "tokenId": 16, "registeredAt": "2025-07-23T09:05:08.016Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:24.797Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19299405281", "tokenId": 17, "registeredAt": "2025-07-23T09:05:33.816Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:25.644Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "16233220647", "tokenId": 18, "registeredAt": "2025-07-23T09:05:59.931Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:26.477Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259463870", "tokenId": 19, "registeredAt": "2025-07-23T09:06:21.892Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:27.313Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19269445804", "tokenId": 20, "registeredAt": "2025-07-23T09:08:09.249Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:28.143Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259463587", "tokenId": 21, "registeredAt": "2025-07-23T09:08:41.507Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:28.957Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19239452436", "tokenId": 22, "registeredAt": "2025-07-23T09:09:03.008Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:29.802Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19224092935", "tokenId": 6, "registeredAt": "2025-07-25T01:50:13.371Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:30.574Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19237284129", "tokenId": 7, "registeredAt": "2025-07-25T01:50:39.247Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:31.397Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19209478302", "tokenId": 8, "registeredAt": "2025-07-25T01:51:01.455Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:32.195Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19239453203", "tokenId": 9, "registeredAt": "2025-07-25T01:51:26.928Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:33.045Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19269465816", "tokenId": 10, "registeredAt": "2025-07-25T01:51:53.173Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:33.987Z"}, {"phone": "19219302169", "tokenId": 11, "registeredAt": "2025-07-25T01:52:18.623Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:34.950Z"}, {"phone": "17042059283", "tokenId": 12, "registeredAt": "2025-07-25T01:52:44.393Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:35.901Z"}, {"phone": "19269442712", "tokenId": 13, "registeredAt": "2025-07-25T01:53:06.362Z", "tokenValid": true, "lastTokenTest": "2025-07-25T08:29:36.908Z"}, {"phone": "19219392409", "tokenId": 5, "registeredAt": "2025-07-25T08:30:31.987Z"}, {"phone": "19216896539", "tokenId": 6, "registeredAt": "2025-07-25T08:31:02.564Z"}, {"phone": "19297003462", "tokenId": 7, "registeredAt": "2025-07-25T09:11:04.413Z"}], "lastUpdated": "2025-07-25T09:11:04.414Z", "totalCount": 32, "description": "已注册的手机号列表"}