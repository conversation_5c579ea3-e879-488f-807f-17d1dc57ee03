{"phones": [{"phone": "19239094464", "tokenId": 6, "registeredAt": "2025-07-11T03:23:39.454Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:08:52.801Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19292390449", "tokenId": 7, "registeredAt": "2025-07-11T03:24:10.028Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:08:53.609Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19289097564", "tokenId": 8, "registeredAt": "2025-07-11T03:25:46.197Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:08:54.419Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19292447202", "tokenId": 9, "registeredAt": "2025-07-11T03:26:11.154Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:08:55.257Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19212590157", "tokenId": 10, "registeredAt": "2025-07-11T03:27:58.190Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:08:56.076Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19292543836", "tokenId": 11, "registeredAt": "2025-07-11T03:32:07.753Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:08:56.836Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259464063", "tokenId": 8, "registeredAt": "2025-07-22T11:12:42.935Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:08:57.660Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259463059", "tokenId": 9, "registeredAt": "2025-07-22T11:13:08.336Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:08:58.438Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19269468919", "tokenId": 10, "registeredAt": "2025-07-22T11:13:34.994Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:08:59.221Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "13097461853", "tokenId": 11, "registeredAt": "2025-07-22T11:13:56.587Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:00.050Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259464256", "tokenId": 12, "registeredAt": "2025-07-22T11:14:21.989Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:00.873Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259464036", "tokenId": 13, "registeredAt": "2025-07-22T11:14:48.178Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:01.690Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19289648504", "tokenId": 14, "registeredAt": "2025-07-23T09:03:27.457Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:02.550Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19211649140", "tokenId": 15, "registeredAt": "2025-07-23T09:04:37.832Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:03.348Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "16237200807", "tokenId": 16, "registeredAt": "2025-07-23T09:05:08.016Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:04.156Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19299405281", "tokenId": 17, "registeredAt": "2025-07-23T09:05:33.816Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:04.986Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "16233220647", "tokenId": 18, "registeredAt": "2025-07-23T09:05:59.931Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:05.833Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259463870", "tokenId": 19, "registeredAt": "2025-07-23T09:06:21.892Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:06.620Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19269445804", "tokenId": 20, "registeredAt": "2025-07-23T09:08:09.249Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:07.426Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19259463587", "tokenId": 21, "registeredAt": "2025-07-23T09:08:41.507Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:08.213Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19239452436", "tokenId": 22, "registeredAt": "2025-07-23T09:09:03.008Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:09.023Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19224092935", "tokenId": 6, "registeredAt": "2025-07-25T01:50:13.371Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:09.847Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19237284129", "tokenId": 7, "registeredAt": "2025-07-25T01:50:39.247Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:10.626Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19209478302", "tokenId": 8, "registeredAt": "2025-07-25T01:51:01.455Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:11.392Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19239453203", "tokenId": 9, "registeredAt": "2025-07-25T01:51:26.928Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:12.231Z", "tokenTestError": "Token校验失败 - code: 403"}, {"phone": "19269465816", "tokenId": 10, "registeredAt": "2025-07-25T01:51:53.173Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:13.271Z"}, {"phone": "19219302169", "tokenId": 11, "registeredAt": "2025-07-25T01:52:18.623Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:14.252Z"}, {"phone": "17042059283", "tokenId": 12, "registeredAt": "2025-07-25T01:52:44.393Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:15.251Z"}, {"phone": "19269442712", "tokenId": 13, "registeredAt": "2025-07-25T01:53:06.362Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:16.287Z"}, {"phone": "19219392409", "tokenId": 5, "registeredAt": "2025-07-25T08:30:31.987Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:17.199Z"}, {"phone": "19216896539", "tokenId": 6, "registeredAt": "2025-07-25T08:31:02.564Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:18.220Z"}, {"phone": "19297003462", "tokenId": 7, "registeredAt": "2025-07-25T09:11:04.413Z", "tokenValid": true, "lastTokenTest": "2025-07-28T02:09:19.209Z"}], "lastUpdated": "2025-07-28T02:09:19.209Z", "totalCount": 32, "description": "已注册的手机号列表"}